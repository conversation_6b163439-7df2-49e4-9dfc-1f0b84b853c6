import type { MenuGroup } from '../types/menu';

export const MenuGroups: MenuGroup[] = [
  {
    name: '管理',
    menus: [
      {
        name: '用户管理',
        component: 'UserView',
        icon: 'user',
      },
      {
        name: '角色管理',
        component: 'RoleView',
        icon: 'setting',
      },
      {
        name: '菜单管理',
        component: 'MenuView',
        icon: 'consist',
      },
      {
        name: '产品管理',
        component: 'ProductView',
        icon: 'category',
      },
      {
        name: '账号管理',
        component: 'AccountView',
        icon: 'account',
      },
      {
        name: '机构管理',
        component: 'OrgView',
        icon: 'building',
      },
      {
        name: '经纪商管理',
        component: 'BrokerView',
        icon: 'home',
      },
      {
        name: '交易通道管理',
        component: 'TerminalView',
        icon: 'consist',
      },
    ],
  },
  {
    name: '风控',
    menus: [
      {
        name: '风控模板设置',
        component: 'RiskTemplateView',
        icon: 'block',
      },
      {
        name: '预警消息',
        component: 'RiskAlarmView',
        icon: 'exchange',
      },
      {
        name: '审核',
        component: 'RiskTemplateAuditView',
        icon: 'account',
      },
    ],
  },
  {
    name: '概览',
    menus: [
      {
        name: '产品概览',
        component: 'ProductSummaryView',
        icon: 'category',
      },
    ],
  },
  {
    name: '交易',
    menus: [
      {
        name: '普通交易',
        component: 'NormalTradeView',
        icon: 'exchange',
      },
      {
        name: '算法交易',
        component: 'AlgorithmTradeView',
        icon: 'exchange',
      },
    ],
  },
];
