<script setup lang="tsx">
import { onMounted, shallowRef, computed } from 'vue';
import { ElMessage, ElMessageBox, ElTree } from 'element-plus';
import type { MomMenu, MomMenuTree } from '../../../../xtrade-sdk/dist';
import { AdminService } from '@/api';
import MenuDialog from './MenuDialog.vue';
import PermissionDialog from './PermissionDialog.vue';

// 菜单树数据
const menuTreeData = shallowRef<MomMenuTree[]>([]);
const selectedMenu = shallowRef<MomMenuTree | undefined>();

// 对话框状态
const menuDialogVisible = shallowRef(false);
const permissionDialogVisible = shallowRef(false);
const editingMenu = shallowRef<MomMenu | undefined>();
const parentMenuId = shallowRef<number | undefined>();

// 树形组件引用
const treeRef = shallowRef<InstanceType<typeof ElTree>>();

// 树形配置
const treeProps = {
  children: 'children',
  label: 'menuName',
  value: 'id',
};

// 加载菜单树数据
const loadMenuTree = async () => {
  try {
    const data = await AdminService.getMenuTree();
    menuTreeData.value = data;
  } catch (error) {
    ElMessage.error('加载菜单数据失败');
    console.error(error);
  }
};

// 处理节点点击
const handleNodeClick = (data: MomMenuTree) => {
  selectedMenu.value = data;
};

// 创建菜单
const handleCreateMenu = (parentId?: number) => {
  editingMenu.value = undefined;
  parentMenuId.value = parentId;
  menuDialogVisible.value = true;
};

// 编辑菜单
const handleEditMenu = (menu: MomMenuTree) => {
  editingMenu.value = menu;
  parentMenuId.value = menu.parentMenuId;
  menuDialogVisible.value = true;
};

// 删除菜单
const handleDeleteMenu = async (menu: MomMenuTree) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单"${menu.menuName}"吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    await AdminService.deleteMenu(menu.id);
    ElMessage.success('删除成功');
    await loadMenuTree();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
      console.error(error);
    }
  }
};

// 权限管理
const handlePermissionManagement = (menu: MomMenuTree) => {
  selectedMenu.value = menu;
  permissionDialogVisible.value = true;
};

// 菜单保存成功回调
const handleMenuSaved = () => {
  menuDialogVisible.value = false;
  loadMenuTree();
};

// 权限保存成功回调
const handlePermissionSaved = () => {
  permissionDialogVisible.value = false;
};

// 渲染树节点
const renderTreeNode = ({ node, data }: { node: any; data: MomMenuTree }) => {
  return (
    <div class="tree-node flex aic jcsb w-full">
      <div class="flex aic">
        <span>{data.menuName}</span>
        <span class="menu-id ml-8 text-gray-400">#{data.id}</span>
      </div>
      <div class="node-actions flex aic gap-4">
        <el-button
          size="small"
          type="primary"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handleCreateMenu(data.id);
          }}
        >
          <i class="iconfont icon-add-new"></i>
        </el-button>
        <el-button
          size="small"
          type="primary"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handleEditMenu(data);
          }}
        >
          <i class="iconfont icon-setting"></i>
        </el-button>
        <el-button
          size="small"
          type="primary"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handlePermissionManagement(data);
          }}
        >
          权限
        </el-button>
        <el-button
          size="small"
          type="danger"
          link
          onClick={(e: Event) => {
            e.stopPropagation();
            handleDeleteMenu(data);
          }}
        >
          <i class="iconfont icon-remove"></i>
        </el-button>
      </div>
    </div>
  );
};

onMounted(() => {
  loadMenuTree();
});
</script>

<template>
  <div class="menu-management" h-full flex flex-col>
    <!-- 头部操作栏 -->
    <div class="header-actions" flex aic jcsb p-16 bg="[--g-block-bg-5]">
      <h3 m-0>菜单管理</h3>
      <div flex aic gap-8>
        <el-button @click="loadMenuTree">
          <i class="iconfont icon-refresh" mr-4></i>
          刷新
        </el-button>
        <el-button type="primary" @click="handleCreateMenu()">
          <i class="iconfont icon-add-new" mr-4></i>
          创建菜单
        </el-button>
      </div>
    </div>

    <!-- 菜单树 -->
    <div class="tree-container" flex-1 p-16 overflow-auto>
      <el-tree
        ref="treeRef"
        :data="menuTreeData"
        :props="treeProps"
        node-key="id"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <component :is="renderTreeNode" :node="node" :data="data" />
        </template>
      </el-tree>
    </div>

    <!-- 菜单编辑对话框 -->
    <MenuDialog
      v-model:visible="menuDialogVisible"
      :menu="editingMenu"
      :parent-menu-id="parentMenuId"
      :menu-tree="menuTreeData"
      @saved="handleMenuSaved"
    />

    <!-- 权限管理对话框 -->
    <PermissionDialog
      v-model:visible="permissionDialogVisible"
      :menu="selectedMenu"
      @saved="handlePermissionSaved"
    />
  </div>
</template>

<style scoped>
.menu-management {
  background: var(--g-bg-1);
}

.tree-node {
  padding: 4px 8px;
  border-radius: 4px;
}

.tree-node:hover {
  background: var(--g-block-bg-3);
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.node-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.menu-id {
  font-size: 12px;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

:deep(.el-tree-node__expand-icon) {
  padding: 6px;
}
</style>
