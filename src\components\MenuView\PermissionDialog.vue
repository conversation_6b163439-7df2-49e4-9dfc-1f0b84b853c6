<script setup lang="tsx">
import { computed, onMounted, shallowRef, watch } from 'vue';
import { ElMessage, ElMessageBox, TableV2SortOrder } from 'element-plus';
import type { MomMenuTree, MomPermission, FormPermission } from '../../../../xtrade-sdk/dist';
import type { ColumnDefinition, RowAction } from '@/types';
import { AdminService } from '@/api';
import { Formatter } from '@/script';
import VirtualizedTable from '../common/VirtualizedTable.vue';
import PermissionEditDialog from './PermissionEditDialog.vue';

interface Props {
  visible: boolean;
  menu?: MomMenuTree;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'saved'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 权限列表数据
const permissions = shallowRef<MomPermission[]>([]);

// 权限编辑对话框状态
const permissionEditVisible = shallowRef(false);
const editingPermission = shallowRef<MomPermission | undefined>();

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value),
});

const dialogTitle = computed(() => (props.menu ? `权限管理 - ${props.menu.menuName}` : '权限管理'));

// 表格列定义
const columns: ColumnDefinition<MomPermission> = [
  { key: 'id', title: '权限ID', width: 80, sortable: true },
  { key: 'permissionName', title: '权限标识', width: 150, sortable: true },
  { key: 'permissionZhName', title: '权限名称', width: 150, sortable: true },
  { key: 'functionCode', title: '功能码', width: 100, sortable: true },
  { key: 'method', title: '请求方式', width: 100, sortable: true },
  { key: 'url', title: 'URL地址', width: 200, sortable: true },
  {
    key: 'defaultPermission',
    title: '默认权限',
    width: 100,
    sortable: true,
    cellRenderer: ({ rowData }: { rowData: MomPermission }) => {
      return <div>{rowData.defaultPermission === 1 ? '是' : '否'}</div>;
    },
  },
];

// 行操作
const rowActions: RowAction<MomPermission>[] = [
  {
    label: '编辑',
    icon: 'setting',
    onClick: row => {
      handleEditPermission(row);
    },
  },
  {
    label: '删除',
    icon: 'remove',
    onClick: row => {
      handleDeletePermission(row);
    },
  },
];

// 加载权限列表
const loadPermissions = async () => {
  if (!props.menu) return;

  try {
    const data = await AdminService.getPermissionsByMenu(props.menu.id);
    permissions.value = data;
  } catch (error) {
    ElMessage.error('加载权限数据失败');
    console.error(error);
  }
};

// 创建权限
const handleCreatePermission = () => {
  editingPermission.value = undefined;
  permissionEditVisible.value = true;
};

// 编辑权限
const handleEditPermission = (permission: MomPermission) => {
  editingPermission.value = permission;
  permissionEditVisible.value = true;
};

// 删除权限
const handleDeletePermission = async (permission: MomPermission) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限"${permission.permissionZhName}"吗？删除后将无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    );

    await AdminService.deletePermission(permission.id);
    ElMessage.success('删除成功');
    await loadPermissions();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
      console.error(error);
    }
  }
};

// 权限保存成功回调
const handlePermissionSaved = () => {
  permissionEditVisible.value = false;
  loadPermissions();
};

// 监听对话框显示状态和菜单变化
watch([() => props.visible, () => props.menu], ([visible, menu]) => {
  if (visible && menu) {
    loadPermissions();
  }
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
  >
    <div class="permission-management" h-600>
      <VirtualizedTable
        :sort="{ key: 'createTime', order: TableV2SortOrder.DESC }"
        :columns="columns"
        :data="permissions"
        :row-actions="rowActions"
        :row-action-width="120"
        select
        fixed
      >
        <template #actions>
          <div class="actions" flex aic>
            <el-button link size="small" class="typical-text-button" @click="loadPermissions">
              <i class="iconfont icon-refresh"></i>
              <span>刷新</span>
            </el-button>
            <el-button type="primary" @click="handleCreatePermission">
              <i class="iconfont icon-add-new" mr-5></i>
              <span>新建权限</span>
            </el-button>
          </div>
        </template>
      </VirtualizedTable>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>

    <!-- 权限编辑对话框 -->
    <PermissionEditDialog
      v-model:visible="permissionEditVisible"
      :permission="editingPermission"
      :menu-id="menu?.id"
      @saved="handlePermissionSaved"
    />
  </el-dialog>
</template>

<style scoped>
.permission-management {
  background: var(--g-bg-1);
}
</style>
