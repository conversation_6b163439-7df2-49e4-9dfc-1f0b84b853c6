import { BaseRepo } from '../modules/base-repo.js';
import {
    FormMenu,
    FormOrganization,
    FormPermission,
    FormRole,
    FormUser,
    MomBroker,
    MomMenu,
    MomMenuTree,
    MomOrganization,
    MomPermission,
    MomRole,
    MomTerminal,
    MomUser,
    UpdateUser,
    UserPasswordChange,
    UserPasswordReset,
} from '../types/table/admin.js';

export class AdminRepo extends BaseRepo {
    /**
     * 查询系统用户信息
     */
    async QueryUsers() {
        return await this.assist.Get<MomUser[]>('user/list');
    }

    /**
     * 根据用户ID查询用户详情
     */
    async QueryUserById(user_id: number) {
        return await this.assist.Get<MomUser>('user/detail', { user_id });
    }

    /**
     * 创建系统用户
     */
    async CreateUser(user: FormUser) {
        return await this.assist.Post<MomUser>('user', {}, user);
    }

    /**
     * 更新系统用户
     */
    async UpdateUser(user: UpdateUser & { id: number }) {
        return await this.assist.Put<MomUser>('user', {}, user);
    }

    /**
     * 删除系统用户
     */
    async DeleteUser(user_id: number) {
        return await this.assist.Delete<[]>('user', { user_id });
    }

    /**
     * 重置用户密码
     */
    async ResetUserPassword(data: UserPasswordReset) {
        return await this.assist.Put<[]>('user/password/reset', {}, data);
    }

    /**
     * 修改用户密码
     */
    async ChangeUserPassword(data: UserPasswordChange) {
        return await this.assist.Put<[]>('user/password/change', {}, data);
    }

    /**
     * 启用/禁用用户
     */
    async ToggleUserStatus(user_id: number, status: number) {
        return await this.assist.Put<[]>('user/status', {}, { user_id, status });
    }

    /**
     * 解冻用户
     */
    async UnfreezeUser(user_id: number) {
        return await this.assist.Put<[]>('user/unfreeze', {}, { user_id });
    }

    /**
     * 查询系统机构列表
     */
    async QueryOrgs() {
        return await this.assist.Get<MomOrganization[]>('org/list');
    }

    /**
     * 创建系统机构
     */
    async CreateOrg(org: FormOrganization) {
        return await this.assist.Post<MomOrganization>('org', {}, org);
    }

    /**
     * 更新系统机构
     */
    async UpdateOrg(org: MomOrganization) {
        return await this.assist.Put<[]>('org', {}, org);
    }

    /**
     * 删除系统机构
     */
    async DeleteOrg(org_id: number) {
        return await this.assist.Delete<[]>('org', { org_id });
    }

    /**
     * 查询系统角色列表
     */
    async QueryRoles() {
        return await this.assist.Get<MomRole[]>('role');
    }

    /**
     * 根据角色ID查询角色详情
     */
    async QueryRoleById(role_id: number) {
        return await this.assist.Get<MomRole>('role/detail', { role_id });
    }

    /**
     * 创建系统角色
     */
    async CreateRole(role: FormRole) {
        return await this.assist.Post<MomRole>(
            'role',
            {},
            {
                ...role,
                activeFlag: true,
            },
        );
    }

    /**
     * 更新系统角色
     */
    async UpdateRole(role: MomRole) {
        return await this.assist.Put<MomRole>('role', {}, role);
    }

    /**
     * 删除系统角色
     */
    async DeleteRole(role_id: number) {
        return await this.assist.Delete<[]>('role', { role_id });
    }

    /**
     * 启用/禁用角色
     */
    async ToggleRoleStatus(role_id: number, activeFlag: boolean) {
        return await this.assist.Put<[]>('role/status', {}, { role_id, activeFlag });
    }

    /**
     * 查询角色下的用户列表
     */
    async QueryUsersByRole(role_id: number) {
        return await this.assist.Get<MomUser[]>('role/users', { role_id });
    }

    /**
     * 查询全量菜单列表
     */
    async QueryMenus() {
        return await this.assist.Get<MomMenu[]>('menu');
    }

    /** 查询全量菜单列表（树形） */
    async QueryMenuTree() {
        return await this.assist.Get<MomMenuTree[]>('menu/tree');
    }

    /**
     * 创建系统菜单
     */
    async CreateMenu(menu: FormMenu) {
        return await this.assist.Post<MomMenu>('menu/create', {}, menu);
    }

    /**
     * 更新系统菜单
     */
    async UpdateMenu(menu: MomMenu) {
        return await this.assist.Put<MomMenu>('menu/edit', {}, menu);
    }

    /**
     * 删除系统菜单
     */
    async DeleteMenu(menu_id: number) {
        return await this.assist.Delete<null>('menu/delete', { menu_id });
    }

    /**
     * 查询角色菜单
     */
    async QueryRoleMenu(role_id: number) {
        return await this.assist.Get<MomMenuTree[]>('menu/getRoleMenu', { role_id });
    }

    /**
     * 保存角色菜单权限
     */
    async SaveRoleMenuPermissions(
        role_id: number,
        data: { menuId: number; permissionIds: number[] }[],
    ) {
        return await this.assist.Put<any>(
            'menu/updateRoleMenu',
            { role_id },
            data.map(item => {
                return {
                    ...item,
                    sequence: 0,
                };
            }),
        );
    }

    /**
     * 查询系统券商列表
     */
    async QueryBrokers() {
        return await this.assist.Get<MomBroker[]>('broker/list');
    }

    /**
     * 查询交易终端列表
     */
    async QueryTerminals() {
        return await this.assist.Get<MomTerminal[]>('terminal/list');
    }

    /**
     * 查询当前归属的交易日
     */
    async QueryTradingDay() {
        return await this.assist.Get<string>('tradingDay');
    }

    // ==================== 权限管理相关接口 ====================

    /**
     * 查询系统权限列表
     */
    async QueryPermissions() {
        return await this.assist.Get<MomPermission[]>('permission/list');
    }

    /**
     * 根据菜单ID查询权限列表
     */
    async QueryPermissionsByMenu(menu_id: number) {
        return await this.assist.Get<MomPermission[]>('permission/menu', { menu_id });
    }

    /**
     * 根据用户类型查询权限列表
     */
    async QueryPermissionsByUserType(userType: number) {
        return await this.assist.Get<MomPermission[]>('permission/usertype', { userType });
    }

    /**
     * 创建系统权限
     */
    async CreatePermission(permission: FormPermission) {
        return await this.assist.Post<MomPermission>('permission', {}, permission);
    }

    /**
     * 更新系统权限
     */
    async UpdatePermission(permission: MomPermission) {
        return await this.assist.Put<MomPermission>('permission', {}, permission);
    }

    /**
     * 删除系统权限
     */
    async DeletePermission(permission_id: number) {
        return await this.assist.Delete<[]>('permission', { permission_id });
    }
}
